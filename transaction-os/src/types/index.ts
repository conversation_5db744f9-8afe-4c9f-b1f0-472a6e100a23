// Re-export all types from transaction.ts
export * from './transaction';

// Additional utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Form state types
export interface FormState<T> {
  data: T;
  errors: Record<string, string>;
  isSubmitting: boolean;
  isValid: boolean;
}

// Loading state types
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

// Pagination types
export interface PaginationState {
  page: number;
  limit: number;
  total: number;
  hasMore: boolean;
}

// Sort types
export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

// Filter state types
export interface FilterState<T> {
  filters: T;
  sortConfig: SortConfig;
  pagination: PaginationState;
}
