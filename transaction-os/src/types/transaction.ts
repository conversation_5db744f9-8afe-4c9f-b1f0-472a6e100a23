import { Timestamp } from 'firebase/firestore';

// Transaction status enum
export type TransactionStatus = 
  | 'pending'
  | 'in_progress'
  | 'delayed'
  | 'completed'
  | 'cancelled';

// Company type enum
export type CompanyType = 
  | 'title_company'
  | 'mortgage_lender'
  | 'brokerage'
  | 'inspection_company';

// Bottleneck type enum
export type BottleneckType = 
  | 'documentation_missing'
  | 'title_issue'
  | 'mortgage_delay'
  | 'inspection_delay'
  | 'compliance_issue';

// Bottleneck severity enum
export type BottleneckSeverity = 
  | 'low'
  | 'medium'
  | 'high'
  | 'critical';

// User role enum
export type UserRole = 
  | 'admin'
  | 'manager'
  | 'agent'
  | 'viewer';

// Base interface for Firestore documents
export interface FirestoreDocument {
  id: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// User interface
export interface User extends FirestoreDocument {
  email: string;
  name: string;
  role: UserRole;
  companyId?: string;
}

// Company interface
export interface Company extends FirestoreDocument {
  name: string;
  type: CompanyType;
  apiKey?: string;
  webhookUrl?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  isActive: boolean;
}

// Transaction interface
export interface Transaction extends FirestoreDocument {
  propertyAddress: string;
  propertyValue: number;
  estimatedCloseDate: Timestamp;
  actualCloseDate?: Timestamp;
  status: TransactionStatus;
  companyId: string;
  assignedUserId?: string;
  mlsNumber?: string;
  buyerName?: string;
  sellerName?: string;
  agentName?: string;
  lenderName?: string;
  titleCompanyName?: string;
  notes?: string;
  documents?: TransactionDocument[];
}

// Transaction document interface
export interface TransactionDocument {
  id: string;
  name: string;
  type: string;
  url: string;
  uploadedAt: Timestamp;
  uploadedBy: string;
}

// Bottleneck interface
export interface Bottleneck extends FirestoreDocument {
  type: BottleneckType;
  severity: BottleneckSeverity;
  description: string;
  resolved: boolean;
  transactionId: string;
  resolvedAt?: Timestamp;
  resolvedBy?: string;
  resolutionNotes?: string;
  estimatedResolutionTime?: number; // in hours
}

// Form interfaces for creating/updating
export interface CreateTransactionData {
  propertyAddress: string;
  propertyValue: number;
  estimatedCloseDate: Date;
  companyId: string;
  assignedUserId?: string;
  mlsNumber?: string;
  buyerName?: string;
  sellerName?: string;
  agentName?: string;
  lenderName?: string;
  titleCompanyName?: string;
  notes?: string;
}

export interface UpdateTransactionData extends Partial<CreateTransactionData> {
  status?: TransactionStatus;
  actualCloseDate?: Date;
}

export interface CreateCompanyData {
  name: string;
  type: CompanyType;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  apiKey?: string;
  webhookUrl?: string;
}

export interface UpdateCompanyData extends Partial<CreateCompanyData> {
  isActive?: boolean;
}

// Filter and search interfaces
export interface TransactionFilters {
  status?: TransactionStatus[];
  companyId?: string;
  assignedUserId?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  propertyValueRange?: {
    min: number;
    max: number;
  };
  searchQuery?: string;
}

export interface CompanyFilters {
  type?: CompanyType[];
  isActive?: boolean;
  searchQuery?: string;
}

// Analytics interfaces
export interface TransactionAnalytics {
  totalTransactions: number;
  completedTransactions: number;
  averageCloseTime: number; // in days
  totalValue: number;
  averageValue: number;
  bottleneckCount: number;
  onTimeCompletionRate: number;
  delayedTransactions: number;
}

export interface BottleneckAnalytics {
  totalBottlenecks: number;
  resolvedBottlenecks: number;
  averageResolutionTime: number; // in hours
  bottlenecksByType: Record<BottleneckType, number>;
  bottlenecksBySeverity: Record<BottleneckSeverity, number>;
  criticalBottlenecks: number;
}

// API response interfaces
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
