import { z } from 'zod';
import {
  TransactionStatus,
  CompanyType,
  BottleneckType,
  BottleneckSeverity,
  UserRole
} from '@/types';

// Base validation schemas
export const emailSchema = z.string().email('Please enter a valid email address');
export const phoneSchema = z.string().regex(/^\+?[\d\s\-\(\)]+$/, 'Please enter a valid phone number').optional();
export const urlSchema = z.string().url('Please enter a valid URL').optional();

// User validation schemas
export const userRoleSchema = z.enum(['admin', 'manager', 'agent', 'viewer'] as const);

export const createUserSchema = z.object({
  email: emailSchema,
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters'),
  role: userRoleSchema,
  companyId: z.string().optional(),
});

export const updateUserSchema = createUserSchema.partial();

// Company validation schemas
export const companyTypeSchema = z.enum(['title_company', 'mortgage_lender', 'brokerage', 'inspection_company'] as const);

export const createCompanySchema = z.object({
  name: z.string().min(2, 'Company name must be at least 2 characters').max(200, 'Company name must be less than 200 characters'),
  type: companyTypeSchema,
  address: z.string().max(500, 'Address must be less than 500 characters').optional(),
  phone: phoneSchema,
  email: emailSchema.optional(),
  website: urlSchema,
  apiKey: z.string().optional(),
  webhookUrl: urlSchema,
});

export const updateCompanySchema = createCompanySchema.partial().extend({
  isActive: z.boolean().optional(),
});

// Transaction validation schemas
export const transactionStatusSchema = z.enum(['pending', 'in_progress', 'delayed', 'completed', 'cancelled'] as const);

export const createTransactionSchema = z.object({
  propertyAddress: z.string().min(5, 'Property address must be at least 5 characters').max(500, 'Property address must be less than 500 characters'),
  propertyValue: z.number().min(1000, 'Property value must be at least $1,000').max(*********, 'Property value must be less than $100,000,000'),
  estimatedCloseDate: z.date().min(new Date(), 'Estimated close date must be in the future'),
  companyId: z.string().min(1, 'Please select a company'),
  assignedUserId: z.string().optional(),
  mlsNumber: z.string().max(50, 'MLS number must be less than 50 characters').optional(),
  buyerName: z.string().max(200, 'Buyer name must be less than 200 characters').optional(),
  sellerName: z.string().max(200, 'Seller name must be less than 200 characters').optional(),
  agentName: z.string().max(200, 'Agent name must be less than 200 characters').optional(),
  lenderName: z.string().max(200, 'Lender name must be less than 200 characters').optional(),
  titleCompanyName: z.string().max(200, 'Title company name must be less than 200 characters').optional(),
  notes: z.string().max(2000, 'Notes must be less than 2000 characters').optional(),
});

export const updateTransactionSchema = createTransactionSchema.partial().extend({
  status: transactionStatusSchema.optional(),
  actualCloseDate: z.date().optional(),
});

// Bottleneck validation schemas
export const bottleneckTypeSchema = z.enum(['documentation_missing', 'title_issue', 'mortgage_delay', 'inspection_delay', 'compliance_issue'] as const);
export const bottleneckSeveritySchema = z.enum(['low', 'medium', 'high', 'critical'] as const);

export const createBottleneckSchema = z.object({
  type: bottleneckTypeSchema,
  severity: bottleneckSeveritySchema,
  description: z.string().min(10, 'Description must be at least 10 characters').max(1000, 'Description must be less than 1000 characters'),
  transactionId: z.string().min(1, 'Transaction ID is required'),
  estimatedResolutionTime: z.number().min(1, 'Estimated resolution time must be at least 1 hour').max(720, 'Estimated resolution time must be less than 720 hours').optional(),
});

export const updateBottleneckSchema = createBottleneckSchema.partial().extend({
  resolved: z.boolean().optional(),
  resolvedBy: z.string().optional(),
  resolutionNotes: z.string().max(1000, 'Resolution notes must be less than 1000 characters').optional(),
});

// Filter validation schemas
export const transactionFiltersSchema = z.object({
  status: z.array(transactionStatusSchema).optional(),
  companyId: z.string().optional(),
  assignedUserId: z.string().optional(),
  dateRange: z.object({
    start: z.date(),
    end: z.date(),
  }).optional(),
  propertyValueRange: z.object({
    min: z.number().min(0),
    max: z.number().min(0),
  }).optional(),
  searchQuery: z.string().max(200).optional(),
});

export const companyFiltersSchema = z.object({
  type: z.array(companyTypeSchema).optional(),
  isActive: z.boolean().optional(),
  searchQuery: z.string().max(200).optional(),
});

// Pagination and sorting schemas
export const paginationSchema = z.object({
  page: z.number().min(1, 'Page must be at least 1'),
  limit: z.number().min(1, 'Limit must be at least 1').max(100, 'Limit must be at most 100'),
});

export const sortSchema = z.object({
  field: z.string().min(1, 'Sort field is required'),
  direction: z.enum(['asc', 'desc'] as const),
});

// Authentication schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export const signupSchema = z.object({
  email: emailSchema,
  password: z.string().min(8, 'Password must be at least 8 characters').regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  confirmPassword: z.string(),
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const resetPasswordSchema = z.object({
  email: emailSchema,
});

// Export type inference helpers
export type CreateUserInput = z.infer<typeof createUserSchema>;
export type UpdateUserInput = z.infer<typeof updateUserSchema>;
export type CreateCompanyInput = z.infer<typeof createCompanySchema>;
export type UpdateCompanyInput = z.infer<typeof updateCompanySchema>;
export type CreateTransactionInput = z.infer<typeof createTransactionSchema>;
export type UpdateTransactionInput = z.infer<typeof updateTransactionSchema>;
export type CreateBottleneckInput = z.infer<typeof createBottleneckSchema>;
export type UpdateBottleneckInput = z.infer<typeof updateBottleneckSchema>;
export type TransactionFiltersInput = z.infer<typeof transactionFiltersSchema>;
export type CompanyFiltersInput = z.infer<typeof companyFiltersSchema>;
export type LoginInput = z.infer<typeof loginSchema>;
export type SignupInput = z.infer<typeof signupSchema>;
export type ResetPasswordInput = z.infer<typeof resetPasswordSchema>;