import { 
  collection, 
  doc, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit, 
  Timestamp,
  onSnapshot,
  QueryConstraint
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { User } from '@/types';

const COLLECTION_NAME = 'users';

// Helper function to convert Firestore data to User type
const convertFirestoreToUser = (doc: any): User => {
  const data = doc.data();
  return {
    id: doc.id,
    ...data,
    createdAt: data.createdAt,
    updatedAt: data.updatedAt,
  } as User;
};

// Create or update user profile (used after authentication)
export const createUserProfile = async (
  uid: string, 
  userData: {
    email: string;
    name: string;
    role?: string;
    companyId?: string;
  }
): Promise<void> => {
  try {
    const now = Timestamp.now();
    const userDoc = {
      ...userData,
      role: userData.role || 'viewer',
      createdAt: now,
      updatedAt: now,
    };

    await setDoc(doc(db, COLLECTION_NAME, uid), userDoc);
  } catch (error) {
    console.error('Error creating user profile:', error);
    throw new Error('Failed to create user profile');
  }
};

// Get a single user by ID
export const getUser = async (id: string): Promise<User | null> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return convertFirestoreToUser(docSnap);
    }
    
    return null;
  } catch (error) {
    console.error('Error getting user:', error);
    throw new Error('Failed to get user');
  }
};

// Update user profile
export const updateUserProfile = async (
  id: string, 
  data: Partial<{
    name: string;
    role: string;
    companyId: string;
  }>
): Promise<void> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const updateData = {
      ...data,
      updatedAt: Timestamp.now(),
    };

    await updateDoc(docRef, updateData);
  } catch (error) {
    console.error('Error updating user profile:', error);
    throw new Error('Failed to update user profile');
  }
};

// Delete user profile
export const deleteUserProfile = async (id: string): Promise<void> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting user profile:', error);
    throw new Error('Failed to delete user profile');
  }
};

// Get all users
export const getUsers = async (): Promise<User[]> => {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      orderBy('name', 'asc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(convertFirestoreToUser);
  } catch (error) {
    console.error('Error getting users:', error);
    throw new Error('Failed to get users');
  }
};

// Get users by company
export const getUsersByCompany = async (companyId: string): Promise<User[]> => {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where('companyId', '==', companyId),
      orderBy('name', 'asc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(convertFirestoreToUser);
  } catch (error) {
    console.error('Error getting users by company:', error);
    throw new Error('Failed to get users by company');
  }
};

// Get users by role
export const getUsersByRole = async (role: string): Promise<User[]> => {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where('role', '==', role),
      orderBy('name', 'asc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(convertFirestoreToUser);
  } catch (error) {
    console.error('Error getting users by role:', error);
    throw new Error('Failed to get users by role');
  }
};

// Search users by name or email
export const searchUsers = async (searchQuery: string): Promise<User[]> => {
  try {
    // Search by name
    const nameQuery = query(
      collection(db, COLLECTION_NAME),
      where('name', '>=', searchQuery),
      where('name', '<=', searchQuery + '\uf8ff'),
      orderBy('name'),
      limit(20)
    );

    // Search by email
    const emailQuery = query(
      collection(db, COLLECTION_NAME),
      where('email', '>=', searchQuery),
      where('email', '<=', searchQuery + '\uf8ff'),
      orderBy('email'),
      limit(20)
    );

    const [nameSnapshot, emailSnapshot] = await Promise.all([
      getDocs(nameQuery),
      getDocs(emailQuery)
    ]);

    const nameResults = nameSnapshot.docs.map(convertFirestoreToUser);
    const emailResults = emailSnapshot.docs.map(convertFirestoreToUser);

    // Combine and deduplicate results
    const allResults = [...nameResults, ...emailResults];
    const uniqueResults = allResults.filter((user, index, self) => 
      index === self.findIndex(u => u.id === user.id)
    );

    return uniqueResults;
  } catch (error) {
    console.error('Error searching users:', error);
    throw new Error('Failed to search users');
  }
};

// Real-time listener for users
export const subscribeToUsers = (
  callback: (users: User[]) => void,
  companyId?: string
): (() => void) => {
  try {
    const constraints: QueryConstraint[] = [orderBy('name', 'asc')];

    if (companyId) {
      constraints.push(where('companyId', '==', companyId));
    }

    const q = query(collection(db, COLLECTION_NAME), ...constraints);

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const users = querySnapshot.docs.map(convertFirestoreToUser);
      callback(users);
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error subscribing to users:', error);
    throw new Error('Failed to subscribe to users');
  }
};

// Real-time listener for a single user
export const subscribeToUser = (
  id: string,
  callback: (user: User | null) => void
): (() => void) => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);

    const unsubscribe = onSnapshot(docRef, (docSnap) => {
      if (docSnap.exists()) {
        callback(convertFirestoreToUser(docSnap));
      } else {
        callback(null);
      }
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error subscribing to user:', error);
    throw new Error('Failed to subscribe to user');
  }
};
