import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter, 
  Timestamp,
  onSnapshot,
  QueryConstraint
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { 
  Transaction, 
  CreateTransactionData, 
  UpdateTransactionData,
  TransactionFilters,
  PaginatedResponse 
} from '@/types';

const COLLECTION_NAME = 'transactions';

// Helper function to convert Firestore data to Transaction type
const convertFirestoreToTransaction = (doc: any): Transaction => {
  const data = doc.data();
  return {
    id: doc.id,
    ...data,
    createdAt: data.createdAt,
    updatedAt: data.updatedAt,
    estimatedCloseDate: data.estimatedCloseDate,
    actualCloseDate: data.actualCloseDate || null,
  } as Transaction;
};

// Helper function to convert Date to Timestamp for Firestore
const convertDatesToTimestamps = (data: CreateTransactionData | UpdateTransactionData) => {
  const converted: any = { ...data };
  
  if ('estimatedCloseDate' in converted && converted.estimatedCloseDate instanceof Date) {
    converted.estimatedCloseDate = Timestamp.fromDate(converted.estimatedCloseDate);
  }
  
  if ('actualCloseDate' in converted && converted.actualCloseDate instanceof Date) {
    converted.actualCloseDate = Timestamp.fromDate(converted.actualCloseDate);
  }
  
  return converted;
};

// Create a new transaction
export const createTransaction = async (data: CreateTransactionData): Promise<string> => {
  try {
    const now = Timestamp.now();
    const transactionData = {
      ...convertDatesToTimestamps(data),
      status: 'pending' as const,
      createdAt: now,
      updatedAt: now,
    };

    const docRef = await addDoc(collection(db, COLLECTION_NAME), transactionData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating transaction:', error);
    throw new Error('Failed to create transaction');
  }
};

// Get a single transaction by ID
export const getTransaction = async (id: string): Promise<Transaction | null> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return convertFirestoreToTransaction(docSnap);
    }
    
    return null;
  } catch (error) {
    console.error('Error getting transaction:', error);
    throw new Error('Failed to get transaction');
  }
};

// Update a transaction
export const updateTransaction = async (id: string, data: UpdateTransactionData): Promise<void> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const updateData = {
      ...convertDatesToTimestamps(data),
      updatedAt: Timestamp.now(),
    };

    await updateDoc(docRef, updateData);
  } catch (error) {
    console.error('Error updating transaction:', error);
    throw new Error('Failed to update transaction');
  }
};

// Delete a transaction
export const deleteTransaction = async (id: string): Promise<void> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting transaction:', error);
    throw new Error('Failed to delete transaction');
  }
};

// Get transactions with filtering, sorting, and pagination
export const getTransactions = async (
  filters: TransactionFilters = {},
  page: number = 1,
  pageSize: number = 20,
  sortField: string = 'createdAt',
  sortDirection: 'asc' | 'desc' = 'desc'
): Promise<PaginatedResponse<Transaction>> => {
  try {
    const constraints: QueryConstraint[] = [];

    // Apply filters
    if (filters.status && filters.status.length > 0) {
      constraints.push(where('status', 'in', filters.status));
    }

    if (filters.companyId) {
      constraints.push(where('companyId', '==', filters.companyId));
    }

    if (filters.assignedUserId) {
      constraints.push(where('assignedUserId', '==', filters.assignedUserId));
    }

    if (filters.dateRange) {
      const startDate = Timestamp.fromDate(filters.dateRange.start);
      const endDate = Timestamp.fromDate(filters.dateRange.end);
      constraints.push(where('estimatedCloseDate', '>=', startDate));
      constraints.push(where('estimatedCloseDate', '<=', endDate));
    }

    if (filters.propertyValueRange) {
      constraints.push(where('propertyValue', '>=', filters.propertyValueRange.min));
      constraints.push(where('propertyValue', '<=', filters.propertyValueRange.max));
    }

    // Add sorting
    constraints.push(orderBy(sortField, sortDirection));

    // Add pagination
    constraints.push(limit(pageSize + 1)); // Get one extra to check if there are more

    const q = query(collection(db, COLLECTION_NAME), ...constraints);
    const querySnapshot = await getDocs(q);

    const transactions = querySnapshot.docs.slice(0, pageSize).map(convertFirestoreToTransaction);
    const hasMore = querySnapshot.docs.length > pageSize;

    return {
      data: transactions,
      total: transactions.length, // Note: Firestore doesn't provide total count efficiently
      page,
      limit: pageSize,
      hasMore,
    };
  } catch (error) {
    console.error('Error getting transactions:', error);
    throw new Error('Failed to get transactions');
  }
};

// Search transactions by text
export const searchTransactions = async (
  searchQuery: string,
  page: number = 1,
  pageSize: number = 20
): Promise<PaginatedResponse<Transaction>> => {
  try {
    // Note: Firestore doesn't have full-text search built-in
    // This is a basic implementation that searches in property address
    // For production, consider using Algolia or Elasticsearch
    
    const constraints: QueryConstraint[] = [
      where('propertyAddress', '>=', searchQuery),
      where('propertyAddress', '<=', searchQuery + '\uf8ff'),
      orderBy('propertyAddress'),
      limit(pageSize + 1)
    ];

    const q = query(collection(db, COLLECTION_NAME), ...constraints);
    const querySnapshot = await getDocs(q);

    const transactions = querySnapshot.docs.slice(0, pageSize).map(convertFirestoreToTransaction);
    const hasMore = querySnapshot.docs.length > pageSize;

    return {
      data: transactions,
      total: transactions.length,
      page,
      limit: pageSize,
      hasMore,
    };
  } catch (error) {
    console.error('Error searching transactions:', error);
    throw new Error('Failed to search transactions');
  }
};

// Get transactions by company
export const getTransactionsByCompany = async (companyId: string): Promise<Transaction[]> => {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where('companyId', '==', companyId),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(convertFirestoreToTransaction);
  } catch (error) {
    console.error('Error getting transactions by company:', error);
    throw new Error('Failed to get transactions by company');
  }
};

// Get transactions by user
export const getTransactionsByUser = async (userId: string): Promise<Transaction[]> => {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where('assignedUserId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(convertFirestoreToTransaction);
  } catch (error) {
    console.error('Error getting transactions by user:', error);
    throw new Error('Failed to get transactions by user');
  }
};

// Real-time listener for transactions
export const subscribeToTransactions = (
  callback: (transactions: Transaction[]) => void,
  filters: TransactionFilters = {}
): (() => void) => {
  try {
    const constraints: QueryConstraint[] = [orderBy('createdAt', 'desc')];

    // Apply filters
    if (filters.status && filters.status.length > 0) {
      constraints.push(where('status', 'in', filters.status));
    }

    if (filters.companyId) {
      constraints.push(where('companyId', '==', filters.companyId));
    }

    if (filters.assignedUserId) {
      constraints.push(where('assignedUserId', '==', filters.assignedUserId));
    }

    const q = query(collection(db, COLLECTION_NAME), ...constraints);

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const transactions = querySnapshot.docs.map(convertFirestoreToTransaction);
      callback(transactions);
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error subscribing to transactions:', error);
    throw new Error('Failed to subscribe to transactions');
  }
};

// Real-time listener for a single transaction
export const subscribeToTransaction = (
  id: string,
  callback: (transaction: Transaction | null) => void
): (() => void) => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);

    const unsubscribe = onSnapshot(docRef, (docSnap) => {
      if (docSnap.exists()) {
        callback(convertFirestoreToTransaction(docSnap));
      } else {
        callback(null);
      }
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error subscribing to transaction:', error);
    throw new Error('Failed to subscribe to transaction');
  }
};
