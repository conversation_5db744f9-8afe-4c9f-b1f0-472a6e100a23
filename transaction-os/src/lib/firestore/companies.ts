import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit, 
  Timestamp,
  onSnapshot,
  QueryConstraint
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { 
  Company, 
  CreateCompanyData, 
  UpdateCompanyData,
  CompanyFilters,
  PaginatedResponse 
} from '@/types';

const COLLECTION_NAME = 'companies';

// Helper function to convert Firestore data to Company type
const convertFirestoreToCompany = (doc: any): Company => {
  const data = doc.data();
  return {
    id: doc.id,
    ...data,
    createdAt: data.createdAt,
    updatedAt: data.updatedAt,
  } as Company;
};

// Create a new company
export const createCompany = async (data: CreateCompanyData): Promise<string> => {
  try {
    const now = Timestamp.now();
    const companyData = {
      ...data,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    };

    const docRef = await addDoc(collection(db, COLLECTION_NAME), companyData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating company:', error);
    throw new Error('Failed to create company');
  }
};

// Get a single company by ID
export const getCompany = async (id: string): Promise<Company | null> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return convertFirestoreToCompany(docSnap);
    }
    
    return null;
  } catch (error) {
    console.error('Error getting company:', error);
    throw new Error('Failed to get company');
  }
};

// Update a company
export const updateCompany = async (id: string, data: UpdateCompanyData): Promise<void> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    const updateData = {
      ...data,
      updatedAt: Timestamp.now(),
    };

    await updateDoc(docRef, updateData);
  } catch (error) {
    console.error('Error updating company:', error);
    throw new Error('Failed to update company');
  }
};

// Delete a company
export const deleteCompany = async (id: string): Promise<void> => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);
    await deleteDoc(docRef);
  } catch (error) {
    console.error('Error deleting company:', error);
    throw new Error('Failed to delete company');
  }
};

// Get companies with filtering, sorting, and pagination
export const getCompanies = async (
  filters: CompanyFilters = {},
  page: number = 1,
  pageSize: number = 20,
  sortField: string = 'name',
  sortDirection: 'asc' | 'desc' = 'asc'
): Promise<PaginatedResponse<Company>> => {
  try {
    const constraints: QueryConstraint[] = [];

    // Apply filters
    if (filters.type && filters.type.length > 0) {
      constraints.push(where('type', 'in', filters.type));
    }

    if (filters.isActive !== undefined) {
      constraints.push(where('isActive', '==', filters.isActive));
    }

    // Add sorting
    constraints.push(orderBy(sortField, sortDirection));

    // Add pagination
    constraints.push(limit(pageSize + 1)); // Get one extra to check if there are more

    const q = query(collection(db, COLLECTION_NAME), ...constraints);
    const querySnapshot = await getDocs(q);

    const companies = querySnapshot.docs.slice(0, pageSize).map(convertFirestoreToCompany);
    const hasMore = querySnapshot.docs.length > pageSize;

    return {
      data: companies,
      total: companies.length,
      page,
      limit: pageSize,
      hasMore,
    };
  } catch (error) {
    console.error('Error getting companies:', error);
    throw new Error('Failed to get companies');
  }
};

// Get all active companies (for dropdowns)
export const getActiveCompanies = async (): Promise<Company[]> => {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(convertFirestoreToCompany);
  } catch (error) {
    console.error('Error getting active companies:', error);
    throw new Error('Failed to get active companies');
  }
};

// Search companies by name
export const searchCompanies = async (
  searchQuery: string,
  page: number = 1,
  pageSize: number = 20
): Promise<PaginatedResponse<Company>> => {
  try {
    const constraints: QueryConstraint[] = [
      where('name', '>=', searchQuery),
      where('name', '<=', searchQuery + '\uf8ff'),
      orderBy('name'),
      limit(pageSize + 1)
    ];

    const q = query(collection(db, COLLECTION_NAME), ...constraints);
    const querySnapshot = await getDocs(q);

    const companies = querySnapshot.docs.slice(0, pageSize).map(convertFirestoreToCompany);
    const hasMore = querySnapshot.docs.length > pageSize;

    return {
      data: companies,
      total: companies.length,
      page,
      limit: pageSize,
      hasMore,
    };
  } catch (error) {
    console.error('Error searching companies:', error);
    throw new Error('Failed to search companies');
  }
};

// Get companies by type
export const getCompaniesByType = async (type: string): Promise<Company[]> => {
  try {
    const q = query(
      collection(db, COLLECTION_NAME),
      where('type', '==', type),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(convertFirestoreToCompany);
  } catch (error) {
    console.error('Error getting companies by type:', error);
    throw new Error('Failed to get companies by type');
  }
};

// Real-time listener for companies
export const subscribeToCompanies = (
  callback: (companies: Company[]) => void,
  filters: CompanyFilters = {}
): (() => void) => {
  try {
    const constraints: QueryConstraint[] = [orderBy('name', 'asc')];

    // Apply filters
    if (filters.type && filters.type.length > 0) {
      constraints.push(where('type', 'in', filters.type));
    }

    if (filters.isActive !== undefined) {
      constraints.push(where('isActive', '==', filters.isActive));
    }

    const q = query(collection(db, COLLECTION_NAME), ...constraints);

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const companies = querySnapshot.docs.map(convertFirestoreToCompany);
      callback(companies);
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error subscribing to companies:', error);
    throw new Error('Failed to subscribe to companies');
  }
};

// Real-time listener for a single company
export const subscribeToCompany = (
  id: string,
  callback: (company: Company | null) => void
): (() => void) => {
  try {
    const docRef = doc(db, COLLECTION_NAME, id);

    const unsubscribe = onSnapshot(docRef, (docSnap) => {
      if (docSnap.exists()) {
        callback(convertFirestoreToCompany(docSnap));
      } else {
        callback(null);
      }
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error subscribing to company:', error);
    throw new Error('Failed to subscribe to company');
  }
};
